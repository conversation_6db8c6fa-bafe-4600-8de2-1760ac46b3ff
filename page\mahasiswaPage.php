<?php include 'database.php'; ?>
<main class="dashboard-main">
    <div class="table-container full-width">
        <header>
            <h2>Data Mahasiswa</h2>
            <a href="index.php?page=addMahasiswa.php" class="btn-add">Tambah Mahasiswa</a>
        </header>
        <div style="overflow-x:auto;">
        <table class="display" id="mahasiswaTable">
            <thead>
                <tr>
                    <th>Foto</th>
                    <th>NIM</th>
                    <th>Nama</th>
                    <th>Prodi</th>
                    <th><PERSON><PERSON><PERSON></th>
                    <th>Email</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
            <?php
            $sql = "SELECT m.nim_mahasiswa, m.nama_mahasiswa, p.nama_prodi, j.nama_jurusan, m.email_mahasiswa, m.foto_mahasiswa FROM mahasiswa m JOIN prodi p ON m.prodi_mahasiswa = p.id_prodi JOIN jurusan j ON m.jurusan_mahasiswa = j.id_jurusan";
            $result = $conn->query($sql);
            if ($result && $result->num_rows > 0) {
                while($row = $result->fetch_assoc()) {
                    echo "<tr data-nim='" . htmlspecialchars($row['nim_mahasiswa']) . "'>";
                    echo "<td><img src='" . htmlspecialchars($row['foto_mahasiswa']) . "' alt='Foto' class='profile-photo'></td>";
                    echo "<td>" . htmlspecialchars($row['nim_mahasiswa']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_mahasiswa']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_prodi']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['email_mahasiswa']) . "</td>";
                    echo "<td>"
                        . "<a href='index.php?page=editMahasiswa.php&nim=" . urlencode($row['nim_mahasiswa']) . "' class='btn-edit' title='Edit'><img src='images/assets/edit.png' alt='Edit' style='width:16px;height:16px;vertical-align:middle;'></a> "
                        . "<a href='#' class='btn-delete' title='Delete'><img src='images/assets/eraser.png' alt='Delete' style='width:16px;height:16px;vertical-align:middle;'></a>"
                        . "</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='7'>No data</td></tr>";
            }
            ?>
            </tbody>
        </table>
        </div>
    </div>
</main>

<script>
$(document).ready(function() {
    // Delete mahasiswa functionality
    $('.btn-delete').click(function(e) {
        e.preventDefault();
        const row = $(this).closest('tr');
        const nim = row.data('nim');
        const nama = row.find('td:nth-child(3)').text();

        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: 'Anda akan menghapus mahasiswa "' + nama + '"!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'processLogic/deleteMahasiswa.php',
                    type: 'POST',
                    data: { nim_mahasiswa: nim },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Berhasil!', 'Data mahasiswa berhasil dihapus!', 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Gagal!', 'Gagal menghapus data: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Terjadi kesalahan saat menghapus data.', 'error');
                    }
                });
            }
        });
    });
});
</script>
