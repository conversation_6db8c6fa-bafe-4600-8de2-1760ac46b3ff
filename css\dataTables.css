/* Enhanced DataTables Styling */
.dataTables_wrapper {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.dataTables_wrapper.enhanced-datatable {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

.dataTables_wrapper.enhanced-datatable::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    z-index: 1;
}

/* Top Controls Container */
.dataTables_wrapper .dataTables_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Table Header Controls */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: 0;
}

/* Override default DataTables positioning */
.dataTables_wrapper .dataTables_length {
    float: none !important;
}

.dataTables_wrapper .dataTables_filter {
    float: none !important;
    text-align: left !important;
}

.dataTables_wrapper .dataTables_info {
    float: none !important;
    padding-top: 0 !important;
}

.dataTables_wrapper .dataTables_paginate {
    float: none !important;
    text-align: right !important;
}

.dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    white-space: nowrap;
}

/* Search Input Styling */
.dataTables_wrapper .dataTables_filter {
    position: relative;
}

.dataTables_wrapper .dataTables_filter input[type="search"] {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) var(--spacing-md);
    margin-left: var(--spacing-sm);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    width: 280px;
    background: var(--background-secondary);
    color: var(--text-color);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.dataTables_wrapper .dataTables_filter input[type="search"]:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.dataTables_wrapper .dataTables_filter input[type="search"]::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* Length Select Styling */
.dataTables_wrapper .dataTables_length select {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-sm);
    font-size: 0.875rem;
    background-color: var(--background-secondary);
    color: var(--text-color);
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    min-width: 100px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
}

.dataTables_wrapper .dataTables_length select:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.dataTables_wrapper .dataTables_length select:hover {
    border-color: var(--border-hover);
    background-color: white;
}

/* Table Styling */
table.dataTable {
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    margin: 0 !important;
    border: none;
}

table.dataTable thead th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: var(--spacing-md) var(--spacing-sm);
    border: none;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

table.dataTable thead th:first-child {
    border-top-left-radius: 0;
}

table.dataTable thead th:last-child {
    border-top-right-radius: 0;
}

table.dataTable tbody td {
    padding: var(--spacing-md) var(--spacing-sm);
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-color);
    transition: background-color 0.2s ease;
}

table.dataTable tbody tr {
    transition: background-color 0.2s ease;
}

table.dataTable tbody tr:hover {
    background-color: var(--background-secondary);
}

table.dataTable tbody tr:nth-child(even) {
    background-color: var(--background-tertiary);
}

table.dataTable tbody tr:nth-child(even):hover {
    background-color: var(--background-secondary);
}

/* Action Buttons */
.btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-xs);
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 0.75rem;
}

.btn-edit {
    background-color: var(--success-color);
    color: white;
}

.btn-edit:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    background-color: var(--error-color);
    color: white;
}

.btn-delete:hover, .btn-delete-prodi:hover, .btn-delete-jurusan:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Add Button Styling */
.btn-add {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
}

.btn-add:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

.btn-add::before {
    content: '+';
    font-size: 1.1rem;
    font-weight: bold;
}

/* Bottom Controls Container */
.dataTables_wrapper .dataTables_bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Pagination Styling */
.dataTables_wrapper .dataTables_paginate {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 2px;
    background-color: white;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transform: scale(1.05);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: var(--background-tertiary);
    color: var(--text-muted);
    border-color: var(--border-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
    background-color: var(--background-tertiary);
    color: var(--text-muted);
    border-color: var(--border-color);
}

/* Info Text Styling */
.dataTables_wrapper .dataTables_info {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    background: var(--background-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

/* Enhanced Layout Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    flex-shrink: 0;
}

.dataTables_wrapper .dataTables_filter label {
    margin-right: 0;
}

.dataTables_wrapper .dataTables_length label {
    margin-right: 0;
}

/* Custom styling for better visual hierarchy */
.dataTables_wrapper .dataTables_length label::after {
    content: ":";
    margin-left: var(--spacing-xs);
}

.dataTables_wrapper .dataTables_filter label::after {
    content: ":";
    margin-left: var(--spacing-xs);
}

/* Loading state styling */
.dataTables_wrapper .dataTables_processing {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;
    font-weight: 600 !important;
    color: var(--primary-color) !important;
    box-shadow: var(--shadow-lg) !important;
    z-index: 1001 !important;
}

/* Empty table styling */
.dataTables_wrapper .dataTables_empty {
    text-align: center;
    padding: var(--spacing-xl) !important;
    color: var(--text-muted);
    font-style: italic;
    background: var(--background-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dataTables_wrapper .dataTables_top {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-lg);
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        justify-content: center;
    }

    .dataTables_wrapper .dataTables_bottom {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .dataTables_wrapper {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .dataTables_wrapper .dataTables_filter input[type="search"] {
        width: 100%;
        max-width: 280px;
        margin-left: 0;
        margin-top: var(--spacing-sm);
    }

    .dataTables_wrapper .dataTables_length select {
        margin: 0;
        margin-top: var(--spacing-sm);
    }

    .dataTables_wrapper .dataTables_length label,
    .dataTables_wrapper .dataTables_filter label {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    table.dataTable thead th,
    table.dataTable tbody td {
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }

    .btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
        width: 28px;
        height: 28px;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: var(--spacing-xs) var(--spacing-sm);
        margin: 0 1px;
        font-size: 0.75rem;
        min-width: 32px;
    }

    .dataTables_wrapper .dataTables_info {
        text-align: center;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .dataTables_wrapper .dataTables_filter input[type="search"] {
        width: 100%;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .dataTables_wrapper .dataTables_length select {
        width: 100%;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: var(--spacing-xs);
        margin: 0;
        font-size: 0.7rem;
        min-width: 28px;
    }
}

/* DataTables Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Table Loading Animation */
.dataTables_wrapper {
    animation: fadeInUp 0.6s ease-out;
}

/* Table Row Animations */
table.dataTable tbody tr {
    animation: slideInLeft 0.4s ease-out;
    transition: all 0.3s ease;
}

table.dataTable tbody tr:hover {
    background-color: var(--primary-light) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Staggered Animation for Table Rows */
table.dataTable tbody tr:nth-child(1) { animation-delay: 0.1s; }
table.dataTable tbody tr:nth-child(2) { animation-delay: 0.15s; }
table.dataTable tbody tr:nth-child(3) { animation-delay: 0.2s; }
table.dataTable tbody tr:nth-child(4) { animation-delay: 0.25s; }
table.dataTable tbody tr:nth-child(5) { animation-delay: 0.3s; }
table.dataTable tbody tr:nth-child(6) { animation-delay: 0.35s; }
table.dataTable tbody tr:nth-child(7) { animation-delay: 0.4s; }
table.dataTable tbody tr:nth-child(8) { animation-delay: 0.45s; }
table.dataTable tbody tr:nth-child(9) { animation-delay: 0.5s; }
table.dataTable tbody tr:nth-child(10) { animation-delay: 0.55s; }

/* Action Button Animations */
.btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-edit:hover, .btn-delete:hover, .btn-delete-prodi:hover, .btn-delete-jurusan:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-md);
}

.btn-edit:active, .btn-delete:active, .btn-delete-prodi:active, .btn-delete-jurusan:active {
    transform: translateY(0) scale(0.98);
}

/* Loading Shimmer Effect */
.dataTables_processing {
    background: linear-gradient(90deg,
        var(--background-secondary) 0%,
        var(--background-tertiary) 50%,
        var(--background-secondary) 100%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Pagination Animation */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Search Input Animation */
.dataTables_wrapper .dataTables_filter input[type="search"] {
    transition: all 0.3s ease;
}

.dataTables_wrapper .dataTables_filter input[type="search"]:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Length Select Animation */
.dataTables_wrapper .dataTables_length select {
    transition: all 0.3s ease;
}

.dataTables_wrapper .dataTables_length select:focus {
    transform: scale(1.02);
}

/* Photo styling in tables */
.profile-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.profile-photo:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

/* Additional Animation Classes */
.animate-row {
    animation: slideInLeft 0.4s ease-out;
}

.fade-in-row {
    animation: fadeInUp 0.3s ease-out;
}

.datatable-loaded {
    opacity: 1;
}

/* Smooth transitions for all interactive elements */
.dataTables_wrapper * {
    transition: all 0.2s ease;
}

/* Enhanced hover effects for controls */
.dataTables_wrapper .dataTables_length:hover label,
.dataTables_wrapper .dataTables_filter:hover label {
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Focus states for accessibility */
.dataTables_wrapper .dataTables_filter input[type="search"]:focus,
.dataTables_wrapper .dataTables_length select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Custom scrollbar for table container */
.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
    transition: background 0.2s ease;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Enhanced button hover effects */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--primary-color) !important;
    border: 1px solid var(--primary-color) !important;
    color: white !important;
    transform: scale(1.05);
}

/* Table header animations */
table.dataTable thead th {
    transition: all 0.3s ease;
}

table.dataTable thead th:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
}

/* Loading state improvements */
.dataTables_wrapper.loading {
    position: relative;
}

.dataTables_wrapper.loading::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}