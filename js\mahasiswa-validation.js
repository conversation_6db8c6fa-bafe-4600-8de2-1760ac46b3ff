/**
 * Real-time Mahasiswa Form Validation with Animations
 * Provides real-time validation for email and password fields in add/edit mahasiswa forms
 */

document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.querySelector('input[name="email_mahasiswa"]');
    const passwordInput = document.querySelector('input[name="password_mahasiswa"]');
    const form = document.querySelector('.edit-mahasiswa-form');
    const submitButton = document.querySelector('button[type="submit"]');

    if (!emailInput || !passwordInput || !form) {
        return; // Exit if required elements are not found
    }

    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Password requirements
    const passwordRequirements = {
        minLength: 8,
        hasUpperCase: /[A-Z]/,
        hasLowerCase: /[a-z]/,
        hasNumber: /\d/,
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/
    };

    // Check if this is edit form (password is optional)
    const isEditForm = passwordInput.placeholder && passwordInput.placeholder.includes('Kosongkan');

    // Create password requirements element
    function createPasswordRequirements() {
        const requirementsDiv = document.createElement('div');
        requirementsDiv.className = 'password-requirements';
        requirementsDiv.innerHTML = `
            <ul>
                <li id="req-length">Minimal 8 karakter</li>
                <li id="req-uppercase">Minimal 1 huruf besar</li>
                <li id="req-lowercase">Minimal 1 huruf kecil</li>
                <li id="req-number">Minimal 1 angka</li>
                <li id="req-special">Minimal 1 karakter khusus</li>
            </ul>
        `;
        return requirementsDiv;
    }

    // Create email validation element
    function createEmailValidation() {
        const validationDiv = document.createElement('div');
        validationDiv.className = 'email-validation';
        return validationDiv;
    }

    // Initialize validation elements
    const passwordRequirementsEl = createPasswordRequirements();
    const emailValidationEl = createEmailValidation();

    // Insert validation elements
    passwordInput.parentNode.appendChild(passwordRequirementsEl);
    emailInput.parentNode.appendChild(emailValidationEl);

    // Add form group classes for styling
    emailInput.parentNode.classList.add('form-group');
    passwordInput.parentNode.classList.add('form-group');

    // Email validation
    function validateEmail(email) {
        const isValid = emailRegex.test(email);
        const emailGroup = emailInput.parentNode;
        
        if (email.length === 0) {
            emailGroup.classList.remove('valid', 'invalid');
            emailValidationEl.classList.remove('show');
            return null;
        }

        if (isValid) {
            emailGroup.classList.add('valid');
            emailGroup.classList.remove('invalid');
            emailValidationEl.textContent = 'Email valid ✓';
            emailValidationEl.className = 'email-validation valid show';
        } else {
            emailGroup.classList.add('invalid');
            emailGroup.classList.remove('valid');
            emailValidationEl.textContent = 'Format email tidak valid';
            emailValidationEl.className = 'email-validation invalid show';
        }

        return isValid;
    }

    // Password validation
    function validatePassword(password) {
        const passwordGroup = passwordInput.parentNode;
        
        // For edit forms, empty password is allowed
        if (isEditForm && password.length === 0) {
            passwordGroup.classList.remove('valid', 'invalid');
            passwordRequirementsEl.classList.remove('show');
            return true; // Valid for edit form
        }

        const requirements = {
            length: password.length >= passwordRequirements.minLength,
            uppercase: passwordRequirements.hasUpperCase.test(password),
            lowercase: passwordRequirements.hasLowerCase.test(password),
            number: passwordRequirements.hasNumber.test(password),
            special: passwordRequirements.hasSpecialChar.test(password)
        };

        if (password.length === 0 && !isEditForm) {
            passwordGroup.classList.remove('valid', 'invalid');
            passwordRequirementsEl.classList.remove('show');
            return null;
        }

        // Show requirements when typing
        if (password.length > 0) {
            passwordRequirementsEl.classList.add('show');
        }

        // Update requirement indicators
        document.getElementById('req-length').classList.toggle('valid', requirements.length);
        document.getElementById('req-uppercase').classList.toggle('valid', requirements.uppercase);
        document.getElementById('req-lowercase').classList.toggle('valid', requirements.lowercase);
        document.getElementById('req-number').classList.toggle('valid', requirements.number);
        document.getElementById('req-special').classList.toggle('valid', requirements.special);

        // Check if all requirements are met
        const allValid = Object.values(requirements).every(req => req);

        if (allValid) {
            passwordGroup.classList.add('valid');
            passwordGroup.classList.remove('invalid');
            passwordRequirementsEl.style.borderLeftColor = 'var(--success-color)';
        } else {
            passwordGroup.classList.remove('valid');
            if (password.length > 0) {
                passwordGroup.classList.add('invalid');
            }
            passwordRequirementsEl.style.borderLeftColor = 'var(--warning-color)';
        }

        return allValid;
    }

    // Real-time validation
    emailInput.addEventListener('input', function() {
        validateEmail(this.value.trim());
        updateSubmitButton();
    });

    emailInput.addEventListener('blur', function() {
        validateEmail(this.value.trim());
    });

    passwordInput.addEventListener('input', function() {
        validatePassword(this.value);
        updateSubmitButton();
    });

    passwordInput.addEventListener('focus', function() {
        if (this.value.length > 0 || !isEditForm) {
            passwordRequirementsEl.classList.add('show');
        }
    });

    passwordInput.addEventListener('blur', function() {
        if (this.value.length === 0 && isEditForm) {
            passwordRequirementsEl.classList.remove('show');
        }
    });

    // Update submit button state
    function updateSubmitButton() {
        const emailValid = validateEmail(emailInput.value.trim());
        const passwordValid = validatePassword(passwordInput.value);
        
        // For edit forms, password validation is optional
        const isFormValid = emailValid && (isEditForm ? true : passwordValid);
        
        if (isFormValid) {
            submitButton.classList.remove('disabled');
            submitButton.disabled = false;
        } else {
            submitButton.classList.add('disabled');
            submitButton.disabled = false; // Keep enabled for server-side validation
        }
    }

    // Enhanced form submission
    form.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();
        const password = passwordInput.value;

        // Client-side validation
        const emailValid = validateEmail(email);
        const passwordValid = validatePassword(password);

        // Check required fields
        const requiredFieldsValid = email && (isEditForm || password);

        if (!requiredFieldsValid || !emailValid || (!isEditForm && !passwordValid)) {
            e.preventDefault();
            
            // Show error animation
            if (!email || !emailValid) {
                emailInput.parentNode.classList.add('invalid');
                emailInput.focus();
            }
            if (!isEditForm && (!password || !passwordValid)) {
                passwordInput.parentNode.classList.add('invalid');
                if (email && emailValid) passwordInput.focus();
            }

            // Show alert
            alert('Mohon lengkapi semua field dengan benar sebelum menyimpan.');
            return false;
        }

        // Add loading animation
        submitButton.classList.add('loading');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Menyimpan...';
        submitButton.disabled = true;

        // Add a small delay for better UX
        setTimeout(() => {
            // Form will submit naturally after this
        }, 500);
    });

    // Add smooth transitions to form elements
    const formElements = form.querySelectorAll('input, select, button');
    formElements.forEach((element, index) => {
        element.style.transition = 'all 0.3s ease';
        element.style.animationDelay = `${0.1 + (index * 0.05)}s`;
    });

    // Add focus animations
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(37, 99, 235, 0.15)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Initial validation on page load
    setTimeout(() => {
        if (emailInput.value) {
            validateEmail(emailInput.value.trim());
        }
        if (passwordInput.value) {
            validatePassword(passwordInput.value);
        }
        updateSubmitButton();
    }, 100);
});
