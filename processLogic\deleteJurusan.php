<?php
header('Content-Type: application/json');
include '../database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = isset($_POST['id_jurusan']) ? $_POST['id_jurusan'] : null;
    if ($id) {
        // Check if jurusan is being used by any prodi or mahasiswa
        $checkProdiStmt = $conn->prepare('SELECT COUNT(*) as count FROM prodi WHERE id_jurusan = ?');
        $checkProdiStmt->bind_param('s', $id);
        $checkProdiStmt->execute();
        $checkProdiResult = $checkProdiStmt->get_result();
        $checkProdiRow = $checkProdiResult->fetch_assoc();
        $checkProdiStmt->close();

        $checkMahasiswaStmt = $conn->prepare('SELECT COUNT(*) as count FROM mahasiswa WHERE jurusan_mahasiswa = ?');
        $checkMahasiswaStmt->bind_param('s', $id);
        $checkMahasiswaStmt->execute();
        $checkMahasiswaResult = $checkMahasiswaStmt->get_result();
        $checkMahasiswaRow = $checkMahasiswaResult->fetch_assoc();
        $checkMahasiswaStmt->close();

        if ($checkProdiRow['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Tidak dapat menghapus jurusan karena masih digunakan oleh prodi.']);
        } else if ($checkMahasiswaRow['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Tidak dapat menghapus jurusan karena masih digunakan oleh mahasiswa.']);
        } else {
            $stmt = $conn->prepare('DELETE FROM jurusan WHERE id_jurusan = ?');
            $stmt->bind_param('s', $id);
            if ($stmt->execute()) {
                echo json_encode(['success' => true, 'message' => 'Data jurusan berhasil dihapus.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Gagal menghapus data jurusan.']);
            }
            $stmt->close();
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'ID Jurusan tidak ditemukan.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
