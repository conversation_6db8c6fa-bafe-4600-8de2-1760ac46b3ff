document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginForm = document.querySelector('.login-form');
    const loginButton = document.querySelector('.login-button');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    const passwordRequirements = {
        minLength: 8,
        hasUpperCase: /[A-Z]/,
        hasLowerCase: /[a-z]/,
        hasNumber: /\d/,
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/
    };

    // Create password requirements element
    function createPasswordRequirements() {
        const requirementsDiv = document.createElement('div');
        requirementsDiv.className = 'password-requirements';
        requirementsDiv.innerHTML = `
            <ul>
                <li id="req-length">Minimal 8 karakter</li>
                <li id="req-uppercase">Minimal 1 huruf besar</li>
                <li id="req-lowercase">Minimal 1 huruf kecil</li>
                <li id="req-number">Minimal 1 angka</li>
                <li id="req-special">Minimal 1 karakter khusus</li>
            </ul>
        `;
        return requirementsDiv;
    }

    // Create email validation element
    function createEmailValidation() {
        const validationDiv = document.createElement('div');
        validationDiv.className = 'email-validation';
        return validationDiv;
    }

    // Initialize validation elements
    const passwordRequirementsEl = createPasswordRequirements();
    const emailValidationEl = createEmailValidation();

    // Insert validation elements
    passwordInput.parentNode.appendChild(passwordRequirementsEl);
    emailInput.parentNode.appendChild(emailValidationEl);

    // Email validation
    function validateEmail(email) {
        const isValid = emailRegex.test(email);
        const emailGroup = emailInput.parentNode;
        
        if (email.length === 0) {
            emailGroup.classList.remove('valid', 'invalid');
            emailValidationEl.classList.remove('show');
            return null;
        }

        if (isValid) {
            emailGroup.classList.add('valid');
            emailGroup.classList.remove('invalid');
            emailValidationEl.textContent = 'Email valid ✓';
            emailValidationEl.className = 'email-validation valid show';
        } else {
            emailGroup.classList.add('invalid');
            emailGroup.classList.remove('valid');
            emailValidationEl.textContent = 'Format email tidak valid';
            emailValidationEl.className = 'email-validation invalid show';
        }

        return isValid;
    }

    // Password validation
    function validatePassword(password) {
        const passwordGroup = passwordInput.parentNode;
        const requirements = {
            length: password.length >= passwordRequirements.minLength,
            uppercase: passwordRequirements.hasUpperCase.test(password),
            lowercase: passwordRequirements.hasLowerCase.test(password),
            number: passwordRequirements.hasNumber.test(password),
            special: passwordRequirements.hasSpecialChar.test(password)
        };

        if (password.length === 0) {
            passwordGroup.classList.remove('valid', 'invalid');
            passwordRequirementsEl.classList.remove('show');
            return null;
        }

        // Show requirements
        passwordRequirementsEl.classList.add('show');

        // Update requirement indicators
        document.getElementById('req-length').classList.toggle('valid', requirements.length);
        document.getElementById('req-uppercase').classList.toggle('valid', requirements.uppercase);
        document.getElementById('req-lowercase').classList.toggle('valid', requirements.lowercase);
        document.getElementById('req-number').classList.toggle('valid', requirements.number);
        document.getElementById('req-special').classList.toggle('valid', requirements.special);

        // Check if all requirements are met
        const allValid = Object.values(requirements).every(req => req);

        if (allValid) {
            passwordGroup.classList.add('valid');
            passwordGroup.classList.remove('invalid');
            passwordRequirementsEl.style.borderLeftColor = 'var(--success-color)';
        } else {
            passwordGroup.classList.remove('valid');
            if (password.length > 0) {
                passwordGroup.classList.add('invalid');
            }
            passwordRequirementsEl.style.borderLeftColor = 'var(--warning-color)';
        }

        return allValid;
    }

    // Real-time validation
    emailInput.addEventListener('input', function() {
        validateEmail(this.value.trim());
        updateSubmitButton();
    });

    emailInput.addEventListener('blur', function() {
        validateEmail(this.value.trim());
    });

    passwordInput.addEventListener('input', function() {
        validatePassword(this.value);
        updateSubmitButton();
    });

    passwordInput.addEventListener('focus', function() {
        if (this.value.length > 0) {
            passwordRequirementsEl.classList.add('show');
        }
    });

    // Update submit button state
    function updateSubmitButton() {
        const emailValid = validateEmail(emailInput.value.trim());
        const passwordValid = validatePassword(passwordInput.value);
        
        if (emailValid && passwordValid) {
            loginButton.classList.remove('disabled');
            loginButton.disabled = false;
        } else {
            loginButton.classList.add('disabled');
            loginButton.disabled = false; // Keep enabled for server-side validation
        }
    }

    // Enhanced form submission
    loginForm.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();
        const password = passwordInput.value;

        // Client-side validation
        const emailValid = validateEmail(email);
        const passwordValid = validatePassword(password);

        if (!email || !password) {
            e.preventDefault();
            
            // Show error animation
            if (!email) {
                emailInput.parentNode.classList.add('invalid');
                emailInput.focus();
            }
            if (!password) {
                passwordInput.parentNode.classList.add('invalid');
                if (!email) passwordInput.focus();
            }

            // Show SweetAlert
            Swal.fire({
                title: 'Validasi Gagal',
                text: 'Email dan password harus diisi dengan benar.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Add loading animation
        loginButton.classList.add('loading');
        loginButton.textContent = 'Memproses...';
        loginButton.disabled = true;

        // Add a small delay for better UX
        setTimeout(() => {
            // Form will submit naturally after this
        }, 500);
    });

    // Auto-focus with animation delay
    setTimeout(() => {
        emailInput.focus();
    }, 600);

    // Add smooth transitions to form groups
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.animationDelay = `${0.1 + (index * 0.1)}s`;
    });

    // Handle form errors with animation
    const errorElement = document.querySelector('.login-error');
    if (errorElement) {
        errorElement.classList.add('shake');
        setTimeout(() => {
            errorElement.classList.remove('shake');
        }, 500);
    }

    // Prevent double submission
    let isSubmitting = false;
    loginForm.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        isSubmitting = true;
    });
});
