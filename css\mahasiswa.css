/* Form Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Edit Mahasiswa Form Styles */
.edit-mahasiswa-card {
    max-width: 500px;
    margin: var(--spacing-xl) auto 0 auto;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-xl);
    animation: fadeInScale 0.6s ease-out;
}

.edit-mahasiswa-card header h1,
.edit-mahasiswa-card header h2 {
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
    text-align: center;
    color: var(--text-color);
    font-weight: 600;
}

.edit-mahasiswa-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

/* Form Element Animations */
.edit-mahasiswa-form label {
    animation: slideInUp 0.5s ease-out;
    animation-fill-mode: both;
}

.edit-mahasiswa-form label:nth-child(1) { animation-delay: 0.1s; }
.edit-mahasiswa-form label:nth-child(2) { animation-delay: 0.15s; }
.edit-mahasiswa-form label:nth-child(3) { animation-delay: 0.2s; }
.edit-mahasiswa-form label:nth-child(4) { animation-delay: 0.25s; }
.edit-mahasiswa-form label:nth-child(5) { animation-delay: 0.3s; }
.edit-mahasiswa-form label:nth-child(6) { animation-delay: 0.35s; }
.edit-mahasiswa-form label:nth-child(7) { animation-delay: 0.4s; }
.edit-mahasiswa-form label:nth-child(8) { animation-delay: 0.45s; }

.edit-mahasiswa-form button,
.edit-mahasiswa-form .cancel-btn {
    animation: slideInUp 0.5s ease-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;
}

.edit-mahasiswa-form label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    text-align: left;
    display: block;
    font-size: 0.875rem;
}

.edit-mahasiswa-form input[type="text"],
.edit-mahasiswa-form input[type="email"],
.edit-mahasiswa-form input[type="password"],
.edit-mahasiswa-form input[type="file"],
.edit-mahasiswa-form select {
    width: 100%;
    margin-top: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

/* Enhanced Input Focus Effects */
.edit-mahasiswa-form input[type="text"]:focus,
.edit-mahasiswa-form input[type="email"]:focus,
.edit-mahasiswa-form input[type="password"]:focus,
.edit-mahasiswa-form input[type="file"]:focus,
.edit-mahasiswa-form select:focus {
    border-color: var(--border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.edit-mahasiswa-form input[type="text"]:hover,
.edit-mahasiswa-form input[type="email"]:hover,
.edit-mahasiswa-form input[type="password"]:hover,
.edit-mahasiswa-form select:hover {
    border-color: var(--border-hover);
    transform: translateY(-1px);
}
.edit-mahasiswa-form button[type="submit"],
.edit-mahasiswa-form .cancel-btn {
    grid-column: 1 / -1;
    width: 100%;
    max-width: 300px;
    margin: var(--spacing-md) auto 0 auto;
}

.edit-mahasiswa-form button[type="submit"] {
    font-size: 0.875rem;
    padding: var(--spacing-md) 0;
    border-radius: var(--radius-md);
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.edit-mahasiswa-form button[type="submit"]:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.edit-mahasiswa-form button[type="submit"]:active {
    transform: translateY(0);
}

.edit-mahasiswa-form .cancel-btn {
    background: var(--secondary-color);
    margin-top: var(--spacing-sm);
    text-align: center;
    font-size: 0.875rem;
    padding: var(--spacing-md) 0;
    border-radius: var(--radius-md);
    color: white;
    text-decoration: none;
    display: block;
    transition: all 0.2s ease;
}

.edit-mahasiswa-form .cancel-btn:hover {
    background: var(--secondary-hover);
    transform: translateY(-1px);
}

.edit-mahasiswa-card .error-message {
    color: var(--error-color);
    text-align: center;
    font-size: 0.875rem;
    margin-top: var(--spacing-md);
}
/* Message Styling */
.message {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    font-weight: 500;
    font-size: 0.875rem;
}

.message.success {
    background-color: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.message.error {
    background-color: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.message.warning {
    background-color: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

@media (max-width: 600px) {
    .edit-mahasiswa-card {
        margin: var(--spacing-md) var(--spacing-sm) 0 var(--spacing-sm);
        padding: var(--spacing-lg);
    }

    .edit-mahasiswa-form {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .edit-mahasiswa-form button[type="submit"],
    .edit-mahasiswa-form .cancel-btn {
        grid-column: 1;
        max-width: none;
    }
}
