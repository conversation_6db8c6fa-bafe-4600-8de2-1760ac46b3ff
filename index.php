<?php
session_start();

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_email'])) {
    $_SESSION['login_alert'] = [
        'title' => 'Anda belum login',
        'text' => '<PERSON>lakan login terlebih dahulu',
        'icon' => 'warning'
    ];
    header('Location: login.php');
    exit();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Uji Kompetensi JWD 2025</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/2.3.2/js/dataTables.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/mahasiswa.css">
    <script defer src="js/components/header.js"></script>
    <script defer src="js/components/footer.js"></script>
</head>
<body>
    <!-- Header -->
    <header>
        <h1>Dashboard Manajemen Mahasiswa JWD 2025</h1>
        <nav>
            <ul>
                <li><a href="index.php?page=homePage.php">Home</a></li>
                <li><a href="index.php?page=mahasiswaPage.php">Mahasiswa</a></li>
                <li><a href="index.php?page=prodiPage.php">Prodi</a></li>
                <li><a href="index.php?page=jurusanPage.php">Jurusan</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main -->
    <main>
        <!-- using if else to load page based on active page -->
        <?php if (isset($_GET['page'])) {
            $page = $_GET['page'];
        } else {
            $page = 'homePage.php';
        }
        ?>
        <?php include 'page/' . $page; ?>
    </main>
    <!-- Footer -->
    <app-footer></app-footer>

    <!-- Script -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    </p-footer></app-footer>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.DataTable) {
                // Enhanced DataTable configuration with animations
                const dataTableConfig = {
                    responsive: true,
                    pageLength: 10,
                    lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                    dom: '<"dataTables_top"lf>rt<"dataTables_bottom"ip>',
                    language: {
                        search: "Cari",
                        searchPlaceholder: "Ketik untuk mencari...",
                        lengthMenu: "Tampilkan _MENU_ data per halaman",
                        info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                        infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                        infoFiltered: "(difilter dari _MAX_ total data)",
                        paginate: {
                            first: "Pertama",
                            last: "Terakhir",
                            next: "Selanjutnya",
                            previous: "Sebelumnya"
                        },
                        emptyTable: "Tidak ada data yang tersedia",
                        zeroRecords: "Tidak ada data yang cocok",
                        processing: "Memuat data..."
                    },
                    order: [[1, 'asc']], // Sort by second column (usually ID/NIM)
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable sorting on last column (Actions)
                    ],
                    processing: true,
                    deferRender: true,
                    initComplete: function(settings, json) {
                        // Add animation class after initialization
                        $(this.api().table().container()).addClass('datatable-loaded');

                        // Animate table rows on load
                        $(this.api().table().body()).find('tr').each(function(index) {
                            $(this).css('animation-delay', (index * 0.05) + 's');
                        });

                        // Add placeholder to search input
                        const searchInput = $(this.api().table().container()).find('.dataTables_filter input');
                        searchInput.attr('placeholder', 'Ketik untuk mencari...');

                        // Add custom styling classes
                        $(this.api().table().container()).find('.dataTables_wrapper').addClass('enhanced-datatable');
                    },
                    drawCallback: function(settings) {
                        // Re-animate rows after each draw (pagination, search, etc.)
                        $(this.api().table().body()).find('tr').each(function(index) {
                            $(this).css('animation-delay', (index * 0.05) + 's');
                            $(this).removeClass('animate-row').addClass('animate-row');
                        });

                        // Remove animation class after animation completes
                        setTimeout(() => {
                            $(this.api().table().body()).find('tr').removeClass('animate-row');
                        }, 1000);
                    }
                };

                // Initialize DataTables with enhanced config and animations
                const mahasiswaTable = new DataTable('#mahasiswaTable', dataTableConfig);
                const prodiTable = new DataTable('#prodiTable', dataTableConfig);
                const jurusanTable = new DataTable('#jurusanTable', dataTableConfig);

                // Add smooth transitions for table interactions
                $('table.dataTable').on('draw.dt', function() {
                    $(this).find('tbody tr').addClass('fade-in-row');
                });

                // Enhanced search with debounce for better performance
                let searchTimeout;
                $('.dataTables_filter input').on('keyup', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = this.value;
                    const table = $(this).closest('.dataTables_wrapper').find('table').DataTable();

                    searchTimeout = setTimeout(() => {
                        table.search(searchTerm).draw();
                    }, 300);
                });
            }

            function loadPage(page) {
                const main = document.getElementById('app-main');
                main.innerHTML = '';
                if (page === 'mahasiswa.php') {
                    main.appendChild(document.createElement('mahasiswa-page'));
                } else if (page === 'index.php') {
                    main.appendChild(document.createElement('main-page'));
                } else {
                    main.innerHTML = '<p>Selamat datang di Dashboard!</p>';
                }
            }

            // Load initial page from localStorage or default
            const initialPage = localStorage.getItem('activePage') || 'index.php';
            loadPage(initialPage);

            // Listen for SPA navigation
            window.addEventListener('navigate', function(e) {
                loadPage(e.detail.page);
            });
        });
    </script>
</body>
</html>